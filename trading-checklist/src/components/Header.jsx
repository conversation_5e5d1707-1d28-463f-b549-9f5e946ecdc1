import React from 'react'

const Header = () => {
  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo and Company Name */}
          <div className="flex items-center space-x-4">
            <img 
              src="/companylogo.jpeg" 
              alt="Limitless Options Logo" 
              className="h-12 w-12 rounded-lg object-cover shadow-sm"
            />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Limitless Options
              </h1>
              <p className="text-sm text-gray-600">
                Trading Checklist & Journal
              </p>
            </div>
          </div>

          {/* Current Date Display */}
          <div className="text-right">
            <div className="text-sm text-gray-500">Today</div>
            <div className="text-lg font-semibold text-gray-900">
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
