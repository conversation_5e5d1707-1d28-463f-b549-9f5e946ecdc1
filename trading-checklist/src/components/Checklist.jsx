import React, { useState, useEffect } from 'react'

const Checklist = ({ currentDate }) => {
  // Checklist items based on your requirements
  const checklistItems = [
    {
      id: 'zone_drawn',
      text: 'Zone drawn on 15–30m',
      description: 'Identify and mark key support/resistance zones on 15-30 minute timeframe'
    },
    {
      id: 'price_sweep',
      text: 'Price hits zone — is there a sweep of recent high/low?',
      description: 'Check if price action shows liquidity sweep before reversal'
    },
    {
      id: 'volume_analysis',
      text: 'Is volume rising or fading at the zone?',
      description: 'Analyze volume behavior at key levels for confirmation'
    },
    {
      id: 'structure_flip',
      text: 'Is structure flipping? (Break of market structure)',
      description: 'Look for clear break of market structure indicating trend change'
    },
    {
      id: 'candle_strength',
      text: 'Candle strength: engulfing, rejection wick, etc.',
      description: 'Identify strong reversal candle patterns at key levels'
    },
    {
      id: 'entry_time',
      text: 'Entry time in ideal session (9:30–11am, or 1–2pm)',
      description: 'Trade during high-volume, high-volatility sessions'
    },
    {
      id: 'higher_tf_fvg',
      text: 'Higher time FVG or imbalance present?',
      description: 'Check for Fair Value Gaps or imbalances on higher timeframes'
    }
  ]

  // State for checklist completion
  const [checkedItems, setCheckedItems] = useState({})
  const [isReadyToTrade, setIsReadyToTrade] = useState(false)

  // Load saved checklist for current date
  useEffect(() => {
    const savedChecklist = localStorage.getItem(`checklist_${currentDate}`)
    if (savedChecklist) {
      const parsed = JSON.parse(savedChecklist)
      setCheckedItems(parsed)
    } else {
      setCheckedItems({})
    }
  }, [currentDate])

  // Calculate if ready to trade (3+ items checked)
  useEffect(() => {
    const checkedCount = Object.values(checkedItems).filter(Boolean).length
    setIsReadyToTrade(checkedCount >= 3)
  }, [checkedItems])

  // Handle checkbox changes
  const handleCheckboxChange = (itemId) => {
    const newCheckedItems = {
      ...checkedItems,
      [itemId]: !checkedItems[itemId]
    }
    setCheckedItems(newCheckedItems)
    
    // Save to localStorage
    localStorage.setItem(`checklist_${currentDate}`, JSON.stringify(newCheckedItems))
  }

  // Reset checklist
  const resetChecklist = () => {
    setCheckedItems({})
    localStorage.removeItem(`checklist_${currentDate}`)
  }

  const checkedCount = Object.values(checkedItems).filter(Boolean).length
  const progressPercentage = (checkedCount / checklistItems.length) * 100

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="card p-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Before Entry Checklist
            </h2>
            <p className="text-gray-600">
              Complete your pre-trade analysis. Only execute when 3+ items are checked.
            </p>
          </div>
          <button
            onClick={resetChecklist}
            className="btn-secondary text-sm"
          >
            Reset
          </button>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Progress</span>
            <span>{checkedCount} of {checklistItems.length} completed</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className={`h-3 rounded-full transition-all duration-300 ${
                isReadyToTrade ? 'bg-success-500' : 'bg-primary-500'
              }`}
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Trading Status */}
        <div className={`p-4 rounded-lg border-2 ${
          isReadyToTrade 
            ? 'bg-success-50 border-success-200 text-success-800' 
            : 'bg-warning-50 border-warning-200 text-warning-800'
        }`}>
          <div className="flex items-center space-x-2">
            <span className="text-xl">
              {isReadyToTrade ? '✅' : '⚠️'}
            </span>
            <span className="font-semibold">
              {isReadyToTrade 
                ? 'Ready to Trade! 3+ criteria met.' 
                : `Not ready to trade. Need ${3 - checkedCount} more criteria.`
              }
            </span>
          </div>
        </div>
      </div>

      {/* Checklist Items */}
      <div className="card p-6">
        <div className="space-y-4">
          {checklistItems.map((item) => (
            <div 
              key={item.id}
              className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                checkedItems[item.id] 
                  ? 'bg-success-50 border-success-200' 
                  : 'bg-gray-50 border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  id={item.id}
                  checked={checkedItems[item.id] || false}
                  onChange={() => handleCheckboxChange(item.id)}
                  className="checkbox-custom mt-1"
                />
                <div className="flex-1">
                  <label 
                    htmlFor={item.id}
                    className={`block font-medium cursor-pointer ${
                      checkedItems[item.id] ? 'text-success-800' : 'text-gray-900'
                    }`}
                  >
                    {item.text}
                  </label>
                  <p className={`text-sm mt-1 ${
                    checkedItems[item.id] ? 'text-success-600' : 'text-gray-600'
                  }`}>
                    {item.description}
                  </p>
                </div>
                {checkedItems[item.id] && (
                  <span className="text-success-500 text-xl">✓</span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Execute Trade Button */}
      {isReadyToTrade && (
        <div className="card p-6 text-center">
          <button className="btn-primary text-lg px-8 py-3">
            🚀 Execute Trade
          </button>
          <p className="text-sm text-gray-600 mt-2">
            All criteria met. You're ready to enter your trade!
          </p>
        </div>
      )}
    </div>
  )
}

export default Checklist
