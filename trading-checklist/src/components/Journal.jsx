import React, { useState, useEffect } from 'react'

const Journal = ({ currentDate }) => {
  const [journalEntry, setJournalEntry] = useState('')
  const [savedEntries, setSavedEntries] = useState({})
  const [selectedDate, setSelectedDate] = useState(currentDate)

  // Load journal entries from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('journal_entries')
    if (saved) {
      setSavedEntries(JSON.parse(saved))
    }
  }, [])

  // Load entry for selected date
  useEffect(() => {
    const entry = savedEntries[selectedDate] || ''
    setJournalEntry(entry)
  }, [selectedDate, savedEntries])

  // Save journal entry
  const saveEntry = () => {
    const newEntries = {
      ...savedEntries,
      [selectedDate]: journalEntry
    }
    setSavedEntries(newEntries)
    localStorage.setItem('journal_entries', JSON.stringify(newEntries))
    
    // Show success message (you could add a toast notification here)
    alert('Journal entry saved successfully!')
  }

  // Delete entry
  const deleteEntry = () => {
    if (window.confirm('Are you sure you want to delete this journal entry?')) {
      const newEntries = { ...savedEntries }
      delete newEntries[selectedDate]
      setSavedEntries(newEntries)
      localStorage.setItem('journal_entries', JSON.stringify(newEntries))
      setJournalEntry('')
    }
  }

  // Get list of dates with entries
  const getDatesWithEntries = () => {
    return Object.keys(savedEntries)
      .filter(date => savedEntries[date].trim() !== '')
      .sort((a, b) => new Date(b) - new Date(a)) // Most recent first
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const datesWithEntries = getDatesWithEntries()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="card p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Trading Journal
        </h2>
        <p className="text-gray-600">
          Record your daily trading thoughts, analysis, and lessons learned.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Journal Entry Form */}
        <div className="lg:col-span-2">
          <div className="card p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Journal Entry
              </h3>
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">
                Selected Date: <span className="font-medium">{formatDate(selectedDate)}</span>
              </p>
            </div>

            <textarea
              value={journalEntry}
              onChange={(e) => setJournalEntry(e.target.value)}
              placeholder="Write about your trading day... What setups did you see? What worked? What didn't? Any lessons learned?"
              className="w-full h-64 p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-none"
            />

            <div className="flex justify-between items-center mt-4">
              <div className="text-sm text-gray-500">
                {journalEntry.length} characters
              </div>
              <div className="space-x-3">
                {journalEntry.trim() && (
                  <button
                    onClick={deleteEntry}
                    className="px-4 py-2 text-red-600 hover:text-red-700 font-medium"
                  >
                    Delete
                  </button>
                )}
                <button
                  onClick={saveEntry}
                  disabled={!journalEntry.trim()}
                  className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Save Entry
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Previous Entries Sidebar */}
        <div className="lg:col-span-1">
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Previous Entries
            </h3>
            
            {datesWithEntries.length === 0 ? (
              <p className="text-gray-500 text-sm">
                No journal entries yet. Start writing to see your history here.
              </p>
            ) : (
              <div className="space-y-3">
                {datesWithEntries.map((date) => (
                  <div
                    key={date}
                    onClick={() => setSelectedDate(date)}
                    className={`p-3 rounded-lg cursor-pointer transition-colors duration-200 ${
                      selectedDate === date
                        ? 'bg-primary-100 border-2 border-primary-300'
                        : 'bg-gray-50 hover:bg-gray-100 border-2 border-transparent'
                    }`}
                  >
                    <div className="text-sm font-medium text-gray-900">
                      {new Date(date).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                      })}
                    </div>
                    <div className="text-xs text-gray-600 mt-1 truncate">
                      {savedEntries[date].substring(0, 50)}
                      {savedEntries[date].length > 50 ? '...' : ''}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Quick Tips */}
          <div className="card p-6 mt-6">
            <h4 className="font-semibold text-gray-900 mb-3">💡 Journal Tips</h4>
            <ul className="text-sm text-gray-600 space-y-2">
              <li>• Record your market analysis</li>
              <li>• Note emotional state during trades</li>
              <li>• Document what you learned</li>
              <li>• Track pattern recognition</li>
              <li>• Review weekly for improvement</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Journal
