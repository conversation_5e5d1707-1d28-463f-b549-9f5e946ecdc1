import React, { useState, useEffect } from 'react'

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalDays: 0,
    completedChecklists: 0,
    journalEntries: 0,
    averageCompletion: 0,
    readyToTradeCount: 0,
    recentActivity: []
  })

  useEffect(() => {
    calculateStats()
  }, [])

  const calculateStats = () => {
    // Get all checklist data
    const checklistData = {}
    const journalData = JSON.parse(localStorage.getItem('journal_entries') || '{}')
    
    // Scan localStorage for checklist entries
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('checklist_')) {
        const date = key.replace('checklist_', '')
        const data = JSON.parse(localStorage.getItem(key) || '{}')
        checklistData[date] = data
      }
    }

    const dates = Object.keys(checklistData)
    const totalDays = dates.length
    let completedChecklists = 0
    let readyToTradeCount = 0
    let totalCompletionPercentage = 0

    // Calculate completion stats
    dates.forEach(date => {
      const checklist = checklistData[date]
      const checkedCount = Object.values(checklist).filter(Boolean).length
      const completionPercentage = (checkedCount / 7) * 100
      
      totalCompletionPercentage += completionPercentage
      
      if (checkedCount >= 3) {
        readyToTradeCount++
      }
      
      if (checkedCount === 7) {
        completedChecklists++
      }
    })

    // Get recent activity
    const recentActivity = []
    const sortedDates = dates.sort((a, b) => new Date(b) - new Date(a)).slice(0, 7)
    
    sortedDates.forEach(date => {
      const checklist = checklistData[date]
      const checkedCount = Object.values(checklist).filter(Boolean).length
      const hasJournal = journalData[date] && journalData[date].trim() !== ''
      
      recentActivity.push({
        date,
        checkedCount,
        hasJournal,
        readyToTrade: checkedCount >= 3
      })
    })

    setStats({
      totalDays,
      completedChecklists,
      journalEntries: Object.keys(journalData).filter(date => journalData[date].trim() !== '').length,
      averageCompletion: totalDays > 0 ? Math.round(totalCompletionPercentage / totalDays) : 0,
      readyToTradeCount,
      recentActivity
    })
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    })
  }

  const StatCard = ({ title, value, subtitle, icon, color = 'primary' }) => (
    <div className="card p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className={`text-3xl font-bold text-${color}-600`}>{value}</p>
          {subtitle && <p className="text-sm text-gray-500 mt-1">{subtitle}</p>}
        </div>
        <div className={`text-4xl text-${color}-500`}>{icon}</div>
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="card p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Trading Dashboard
        </h2>
        <p className="text-gray-600">
          Track your trading preparation progress and consistency.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Trading Days"
          value={stats.totalDays}
          subtitle="Days with checklist data"
          icon="📅"
          color="primary"
        />
        <StatCard
          title="Ready to Trade"
          value={stats.readyToTradeCount}
          subtitle={`${stats.totalDays > 0 ? Math.round((stats.readyToTradeCount / stats.totalDays) * 100) : 0}% of days`}
          icon="✅"
          color="success"
        />
        <StatCard
          title="Complete Checklists"
          value={stats.completedChecklists}
          subtitle="All 7 items checked"
          icon="🎯"
          color="primary"
        />
        <StatCard
          title="Journal Entries"
          value={stats.journalEntries}
          subtitle="Days with journal notes"
          icon="📝"
          color="warning"
        />
      </div>

      {/* Progress Overview */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Average Completion Rate
        </h3>
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <div className="w-full bg-gray-200 rounded-full h-4">
              <div 
                className="bg-primary-500 h-4 rounded-full transition-all duration-300"
                style={{ width: `${stats.averageCompletion}%` }}
              ></div>
            </div>
          </div>
          <span className="text-2xl font-bold text-primary-600">
            {stats.averageCompletion}%
          </span>
        </div>
        <p className="text-sm text-gray-600 mt-2">
          Average percentage of checklist items completed per day
        </p>
      </div>

      {/* Recent Activity */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Recent Activity (Last 7 Days)
        </h3>
        
        {stats.recentActivity.length === 0 ? (
          <p className="text-gray-500 text-center py-8">
            No recent activity. Start using the checklist to see your progress here.
          </p>
        ) : (
          <div className="space-y-3">
            {stats.recentActivity.map((activity) => (
              <div
                key={activity.date}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <div className="text-sm font-medium text-gray-900">
                    {formatDate(activity.date)}
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      activity.readyToTrade 
                        ? 'bg-success-100 text-success-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {activity.checkedCount}/7 items
                    </span>
                    {activity.hasJournal && (
                      <span className="px-2 py-1 text-xs bg-warning-100 text-warning-800 rounded-full">
                        Journal ✓
                      </span>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  {activity.readyToTrade ? (
                    <span className="text-success-600 font-medium">Ready to Trade</span>
                  ) : (
                    <span className="text-gray-500">Not Ready</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Tips Section */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          💡 Consistency Tips
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900">Daily Habits</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Complete checklist before market open</li>
              <li>• Review all 7 criteria thoroughly</li>
              <li>• Only trade when 3+ items are met</li>
            </ul>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900">Journal Benefits</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Track emotional patterns</li>
              <li>• Identify recurring mistakes</li>
              <li>• Document successful setups</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
