import { useState, useEffect } from 'react'
import Header from './components/Header'
import Checklist from './components/Checklist'
import Journal from './components/Journal'
import Dashboard from './components/Dashboard'

function App() {
  const [activeTab, setActiveTab] = useState('checklist')
  const [currentDate, setCurrentDate] = useState(new Date().toISOString().split('T')[0])

  // Get today's date for default view
  useEffect(() => {
    setCurrentDate(new Date().toISOString().split('T')[0])
  }, [])

  const renderActiveComponent = () => {
    switch (activeTab) {
      case 'checklist':
        return <Checklist currentDate={currentDate} />
      case 'journal':
        return <Journal currentDate={currentDate} />
      case 'dashboard':
        return <Dashboard />
      default:
        return <Checklist currentDate={currentDate} />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Navigation Tabs */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'checklist', name: 'Daily Checklist', icon: '✅' },
              { id: 'journal', name: 'Trading Journal', icon: '📝' },
              { id: 'dashboard', name: 'Dashboard', icon: '📊' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors duration-200`}
              >
                <span>{tab.icon}</span>
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderActiveComponent()}
      </main>
    </div>
  )
}

export default App
